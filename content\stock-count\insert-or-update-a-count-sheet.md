## Insert or update a count sheet

### Request endpoint

```http
PUT /{companyId}/stock-counts/{stockCountId}/count-sheet
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter    | Type          | Required | Description                   |
|--------------|---------------|----------|-------------------------------|
| companyId    | string <uuid> | Yes      | Your inFlow account companyId |
| stockCountId | string <uuid> | Yes      | The stockCountId to insert or update the count sheet on |

### Request body

**Request body schema:** `application/json`

The count sheet to insert or update

**Notes**:
- `CountSheetId` property is required, please generate a GUID when inserting.

| Field                  | Type                     | Description         |
|------------------------|--------------------------|---------------------|
| assignedToTeamMember   | object (TeamMember)      |                     |
| assignedToTeamMemberId | string <uuid> (Nullable) |                     |
| completedDate          | string <date-time> (Nullable) | The date this sheet was completed |
| countSheetId           | string <uuid>            | The primary identifier for this count sheet. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| isCancelled            | boolean                  | Whether this count sheet is cancelled (being cancelled voids inventory adjustments) |
| isCompleted            | boolean                  | Whether this count sheet is completed (inventory adjustmentments are made when completed) |
| lastModifiedBy         | object (TeamMember)      |                     |
| lastModifiedById       | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this count sheet. This is set automatically, and cannot be set through the API. |
| lines                  | Array of objects         | Lines representing which inventory levels are being adjusted |
| remarks                | string                   | Any extra comments on this count sheet |
| sheetNumber            | integer <int32>          | The number of this sheet as part of the stock count |
| startedDate            | string <date-time> (Nullable) | The date this sheet was started |
| status                 | string                   | The status of this count sheet <br> **Enum:** `"Open"`, `"InProgress"`, `"Completed"` |
| stockCount             | object (StockCount)      |                     |
| stockCountId           | string <uuid>            |                     |
| timestamp              | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

### Payload example

> **WARNING**: The payload sample is 5123 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-a-count-sheet.json](payload-sample-of-insert-or-update-a-count-sheet.json)

### Response

#### Success response (200) schema: `application/json`

| Field                  | Type                     | Description      |
|------------------------|--------------------------|------------------|
| assignedToTeamMember   | object (TeamMember)      |                  |
| assignedToTeamMemberId | string <uuid> (Nullable) |                  |
| completedDate          | string <date-time> (Nullable) | The date this sheet was completed |
| countSheetId           | string <uuid>            | The primary identifier for this count sheet. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| isCancelled            | boolean                  | Whether this count sheet is cancelled (being cancelled voids inventory adjustments) |
| isCompleted            | boolean                  | Whether this count sheet is completed (inventory adjustmentments are made when completed) |
| lastModifiedBy         | object (TeamMember)      |                  |
| lastModifiedById       | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this count sheet. This is set automatically, and cannot be set through the API. |
| lines                  | Array of objects         | Lines representing which inventory levels are being adjusted |
| remarks                | string                   | Any extra comments on this count sheet |
| sheetNumber            | integer <int32>          | The number of this sheet as part of the stock count |
| startedDate            | string <date-time> (Nullable) | The date this sheet was started |
| status                 | string                   | The status of this count sheet <br> **Enum:** `"Open"`, `"InProgress"`, `"Completed"` |
| stockCount             | object (StockCount)      |                  |
| stockCountId           | string <uuid>            |                  |
| timestamp              | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 5123 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-a-count-sheet.json](response-sample-of-insert-or-update-a-count-sheet.json)
