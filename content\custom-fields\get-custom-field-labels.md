## Get Custom Field Labels

### Request endpoint

```http
GET /{companyId}/custom-fields
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |

### Response

#### Success response (200) schema: `application/json`

`customFieldsId` refers to the `companyID`, not `customfieldsID`.

| Field                                 | Type          | Description   |
|---------------------------------------|---------------|---------------|
| customFieldsId                        | string <uuid> | Although the name of the ID is custom fields, this value represents the company ID. |
| purchaseOrderCustomFieldPrintLabels   | `CustomFieldPrintValues` object |  |
| salesOrderCustomFieldPrintLabels      | `CustomFieldPrintValues` object |  |
| stockAdjustmentCustomFieldPrintLabels | `CustomFieldPrintValues` object |  |
| stockTransferCustomFieldPrintLabels   | `CustomFieldPrintValues` object |  |
| workOrderCustomFieldPrintLabels       | `CustomFieldPrintValues` object |  |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "customFieldsId": "********-0000-0000-0000-************",
  "purchaseOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "salesOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "stockAdjustmentCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "stockTransferCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "workOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  }
}
```
