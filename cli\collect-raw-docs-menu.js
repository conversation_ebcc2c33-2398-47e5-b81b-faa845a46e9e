const sidebar = document.querySelector("#redoc-container .menu-content");

const toHumanFriendly = (str) => {
	return str
		.replace(/([a-z])([A-Z])/g, "$1 $2")
		.replace(/[_\-]+/g, " ")
		.replace(/\s+/g, " ")
		.trim()
		.replace(/^./, (s) => s.toUpperCase());
};

const toSlug = (str) => {
	return str
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, "-")
		.replace(/^-+|-+$/g, "");
};

const extractTree = (container) => {
	if (!container) return [];

	const allItems = Array.from(container.querySelectorAll("li[data-item-id]"));
	const topLevelItems = allItems.filter(
		(li) => li.closest("li[data-item-id]") === li
	);

	const buildTree = (li, isTopLevel = false) => {
		const path = li.getAttribute("data-item-id") ?? "";

		const labelSpan = li.querySelector(
			"label > span[title], label > span[width]"
		);
		const rawText = labelSpan?.textContent?.trim() ?? "";
		const text = toHumanFriendly(rawText);
		const slug = toSlug(text);

		const nestedUl = li.querySelector("ul");
		const childItems = nestedUl
			? Array.from(nestedUl.querySelectorAll("li[data-item-id]")).filter(
					(childLi) =>
						childLi.closest("li[data-item-id]") === childLi ||
						childLi.closest("li[data-item-id]") === li
				)
			: [];

		const children = childItems.map((child) => buildTree(child, false));

		// Control property order explicitly
		const node = {
			text,
			path,
			slug,
		};

		if (isTopLevel) {
			node.entity = rawText;
		}

		node.children = children;

		return node;
	};

	return topLevelItems.map((li) => buildTree(li, true));
};

const collectAllPaths = (nodes, set = new Set()) => {
	for (const node of nodes) {
		set.add(node.path);
		collectAllPaths(node.children, set);
	}
	return set;
};

const removeDuplicateTopLevelItems = (tree) => {
	const childPaths = collectAllPaths(tree.flatMap((node) => node.children));
	return tree.filter((node) => !childPaths.has(node.path)); 
};

const sidebarTree = extractTree(sidebar);
const uniqueSidebarTree = removeDuplicateTopLevelItems(sidebarTree);

console.log(uniqueSidebarTree);
