## Get payment terms

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/payment-terms/{paymentTermsId}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter      | Type          | Required | Description                   |
|----------------|---------------|----------|-------------------------------|
| companyId      | string <uuid> | yes      | Your inFlow account companyId |
| paymentTermsId | string <uuid> | yes      | Id of the PaymentTerms to retrieve |

### Response

#### Success response (200) schema: `application/json`

| Field          | Type                | Description |
|----------------|---------------------|-------------|
| daysDue        | integer <int32>     | How many days from invoice until the payment is due |
| isActive       | boolean             | Payment terms `IsActive = false` are deactivated and hidden away for new usage. |
| name           | string              | Payment terms name (human-readable, typically include how many days until due) |
| paymentTermsId | string <uuid>       | The primary identifier for these payment terms. Not shown to users |
| timestamp      | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "daysDue": 30,
  "isActive": true,
  "name": "NET 30",
  "paymentTermsId": "00000000-0000-0000-0000-000000000000",
  "timestamp": "0000000000310AB6"
}
```
