## List categories

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/categories
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `Category` objects. Each `Category` object has the following properties:

| Field          | Type              | Description                     |
|----------------|-------------------|---------------------------------|
| categoryId     | string (uuid)     | The primary identifier for this category. Not shown to users |
| isDefault      | boolean           | Only one category, your company-wide default, should have `IsDefault = true` |
| name           | string            | A human-readable name for this category |
| parentCategory | object (Category) | Parent category (recursive)     |
| parentCategoryId | string (uuid) (Nullable) | ID of the parent category |
| timestamp      | string (rowversion) | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "categoryId": "00000000-0000-0000-0000-000000000000",
    "isDefault": true,
    "name": "Bestsellers",
    "parentCategory": { },
    "parentCategoryId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  }
]
```
