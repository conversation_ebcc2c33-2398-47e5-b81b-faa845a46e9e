/**
 * Simplifies deeply nested HTML by removing unnecessary wrappers,
 * returns a string, and copies the result to the clipboard.
 * @param {string} htmlString - The input HTML string.
 * @returns {string} The simplified HTML string.
 */
async function simplifyAndCopyHtml(htmlString) {
	const template = document.createElement('template');
	template.innerHTML = htmlString.trim();
	const fragment = template.content;

	const isUnnecessaryWrapper = (el) => {
		if (!(el instanceof HTMLElement)) return false;
		if (el.children.length !== 1) return false;

		const child = el.children[0];
		if (!(child instanceof HTMLElement)) return false;

		const insignificant = (node) =>
			node.getAttributeNames().length === 0 &&
			node.classList.length === 0 &&
			node.id === '';

		return (
			el.tagName === child.tagName &&
			insignificant(el) &&
			insignificant(child)
		);
	};

	const unwrapRecursively = (el) => {
		Array.from(el.children).forEach(unwrapRecursively);

		while (isUnnecessaryWrapper(el)) {
			const child = el.firstElementChild;
			el.replaceWith(child);
			el = child;
		}
	};

	Array.from(fragment.children).forEach(unwrapRecursively);

	const resultString = Array.from(fragment.children)
		.map((node) => node.outerHTML)
		.join('\n');

	// Copy to clipboard
	try {
		await navigator.clipboard.writeText(resultString);
		console.log('Simplified HTML copied to clipboard!');
	} catch (err) {
		console.warn('Clipboard copy failed:', err);
	}

	return resultString;
}

(() => {
	document.body.innerHTML = `
		<div style="padding: 1rem; font-family: sans-serif; max-width: 800px; margin: auto;">
			<h2>Paste Your HTML Below</h2>
			<textarea id="htmlInput" style="width: 100%; height: 200px; font-family: monospace;"></textarea>
			<button id="simplifyBtn" style="margin-top: 1rem; padding: 0.5rem 1rem;">Simplify & Copy</button>
			<div id="statusMsg" style="margin-top: 1rem; height: 1.5em; color: green;"></div>
		</div>
	`;

	const statusEl = document.getElementById('statusMsg');
	const textarea = document.getElementById('htmlInput');

	document.getElementById('simplifyBtn').addEventListener('click', async () => {
		const input = textarea.value.trim();
		if (!input) {
			statusEl.textContent = 'Nothing to process.';
			statusEl.style.color = 'orange';
			return;
		}

		const result = await simplifyAndCopyHtml(input);

		if (result !== null) {
			textarea.value = '';
			statusEl.textContent = '✅ HTML simplified & copied to clipboard!';
			statusEl.style.color = 'green';
		} else {
			statusEl.textContent = '❌ Failed to copy to clipboard.';
			statusEl.style.color = 'red';
		}

		// Auto-clear status after 3s
		setTimeout(() => (statusEl.textContent = ''), 3000);
	});
})();
