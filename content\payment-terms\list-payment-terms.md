## List payment terms

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/payment-terms
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of PaymentTerm objects. Each PaymentTerm object has the following properties:

| Field           | Type                | Description             |
|-----------------|---------------------|-------------------------|
| daysDue         | integer <int32>     | How many days from invoice until the payment is due |
| isActive        | boolean             | Payment terms `IsActive = false` are deactivated and hidden away for new usage. |
| name            | string              | Payment terms name (human-readable, typically include how many days until due) |
| paymentTermsId  | string <uuid>       | The primary identifier for these payment terms. Not shown to users |
| timestamp       | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "daysDue": 30,
    "isActive": true,
    "name": "NET 30",
    "paymentTermsId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  }
]
```
