import { readFile, mkdir } from "fs/promises";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

// Resolve __dirname in ESM
const __dirname = dirname(fileURLToPath(import.meta.url));

// === Configuration ===
const INPUT_JSON = resolve(__dirname, "raw-docs-menu.json");
const OUTPUT_DIR = resolve(__dirname, "../content");

// === Main Execution ===
const sidebarTree = JSON.parse(await readFile(INPUT_JSON, "utf8"));
await mkdir(OUTPUT_DIR, { recursive: true });

for (const { slug } of sidebarTree) {
	if (!slug) continue;

	const dirPath = resolve(OUTPUT_DIR, slug);
	if (dirPath.startsWith("section/Overview/")) continue;

	await mkdir(dirPath, { recursive: true });
	console.log(`✅ Created: ${dirPath}`);
}
