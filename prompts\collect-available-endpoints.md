Collect all available API endpoints in this project and save them to the file "content/available-endpoints.md".

Instructions:

1. **Locate Endpoints**:
   - Use "content/docs-menu.md" as a guide to find relevant documentation pages.
   - On each page, look for a section titled "Request endpoint" — this typically contains API details.

2. **Format for Output File ("available-endpoints.md")**:
   - Each top-level link from "docs-menu.md" becomes a top-level section (use Markdown H2: ##).
   - If multiple endpoints are listed under a top-level section, group them into sub-sections (Markdown H3: ###).
   - For each endpoint, include:
     - HTTP method (e.g., GET, POST, etc)
     - URL/endpoint path

Use clear and consistent formatting throughout.
