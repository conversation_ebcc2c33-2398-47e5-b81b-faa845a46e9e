## Insert or update a stock transfer

### Request endpoint

```http
PUT /{companyId}/stock-transfers
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A stock transfer to insert or update.

**Note**:
- `stockTransferId` property is required, please generate a GUID when inserting.
- `fromLocationId` property is required, please provide the location the products will be transferred from.
- `toLocationId` property is required, please provide the location the products will be transferred to.

| Field              | Type                     | Description          |
|--------------------|--------------------------|----------------------|
| assignedToTeamMember | object (TeamMember)    |                      |
| assignedToTeamMemberId | string <uuid> (Nullable) |                  |
| customFields       | object (LargeCustomFieldValues) |               |
| fromLocation       | object (Location)        |                      |
| fromLocationId     | string <uuid>            |                      |
| isCancelled        | boolean                  | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy     | object (TeamMember)      |                      |
| lastModifiedById   | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this stock transfer. This is set automatically, and cannot be set through the API. |
| lines              | Array of objects         | Lines representing which the inventory movements |
| receivedDate       | string <date-time> (Nullable) | The date stock was added to the destination location. |
| remarks            | string                   | Any extra comments on this stock transfer |
| sentDate           | string <date-time> (Nullable) | The date stock was removed from the source location. |
| status             | string                   | The status of the stock transfer (this determines when stock is removed and added)<br>Enum: "Open", "InTransit", "Completed" |
| stockTransferId    | string <uuid>            | The primary identifier for this stock transfer. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| timestamp          | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| toLocation         | object (Location)        |                      |
| toLocationId       | string <uuid>            |                      |
| transferDate       | string <date-time>       | The date this stock transfer was requested. |
| transferNumber     | string                   | An identifier for this stock transfer and shown on printed documents. |

### Payload example

> **WARNING**: The payload sample is 2568 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-a-stock-transfer.json](payload-sample-of-insert-or-update-a-stock-transfer.json)

### Response

#### Success response (200) schema: `application/json`

| Field              | Type                     | Description          |
|--------------------|--------------------------|----------------------|
| assignedToTeamMember | object (TeamMember)    |                      |
| assignedToTeamMemberId  | string <uuid> (Nullable) |                 |
| customFields       | object (LargeCustomFieldValues) |               |
| fromLocation       | object (Location)        |                      |
| fromLocationId     | string <uuid>            |                      |
| isCancelled        | boolean                  | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy     | object (TeamMember)      |                      |
| lastModifiedById   | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this stock transfer. This is set automatically, and cannot be set through the API. |
| lines              | Array of objects         | Lines representing which the inventory movements |
| receivedDate       | string <date-time> (Nullable) | The date stock was added to the destination location. |
| remarks            | string                   | Any extra comments on this stock transfer |
| sentDate           | string <date-time> (Nullable) | The date stock was removed from the source location. |
| status             | string                   | The status of the stock transfer (this determines when stock is removed and added)<br>Enum: "Open", "InTransit", "Completed" |
| stockTransferId    | string <uuid>            | The primary identifier for this stock transfer. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| timestamp          | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| toLocation         | object (Location)        |                      |
| toLocationId       | string <uuid>            |                      |
| transferDate       | string <date-time>       | The date this stock transfer was requested. |
| transferNumber     | string                   | An identifier for this stock transfer and shown on printed documents. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 2568 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-a-stock-transfer.json](response-sample-of-insert-or-update-a-stock-transfer.json)
