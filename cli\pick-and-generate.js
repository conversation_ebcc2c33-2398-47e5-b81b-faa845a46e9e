import { readFile, writeFile } from "fs/promises";
import path, { resolve } from "path";
import { fileURLToPath } from "url";

// Get __dirname in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// === Get CLI arguments ===
const targetSlugs = process.argv.slice(2);
if (targetSlugs.length === 0) {
	console.error("❌ Please provide at least one slug as an argument.");
	console.error("Usage: node pick-and-generate.js products customers");
	process.exit(1);
}

// === Step 1: Load Sidebar JSON ===
const sidebarJsonPath = resolve(__dirname, "raw-docs-menu.json");
const rawSidebar = await readFile(sidebarJsonPath, "utf-8");
const sidebarItems = JSON.parse(rawSidebar);

// === Step 2: Filter top-level items ===
const pickedItems = sidebarItems.filter((item) =>
	targetSlugs.includes(item.slug),
);

if (pickedItems.length === 0) {
	console.error("❌ None of the provided slugs matched top-level items.");
	process.exit(1);
}

// === Step 3: Create browser script ===
const browserScript = `
// This script is meant to be run in the browser console
const sidebarItems = ${JSON.stringify(pickedItems, null, "	")};

/**
 * Recursively scrape sections by path
 */
function scrapeSections(items) {
	let output = '';

	function collectContent(item) {
		const section = document.querySelector(\`.api-content [data-section-id="\${item.path}"]\`);
		if (section) {
			output += section.innerHTML.trim() + '\\n\\n';
		}
		if (Array.isArray(item.children)) {
			item.children.forEach(collectContent);
		}
	}

	items.forEach(collectContent);
	return output.trim();
}

// Run the scraper
let result = scrapeSections(sidebarItems);

// === Post-processing: Remove .redoc-json and its 6-level ancestor ===
(function postProcess() {
	const container = document.createElement('div');
	container.innerHTML = result;

	const redocElements = container.querySelectorAll('.redoc-json');
	redocElements.forEach(el => {
		let target = el;
		for (let i = 0; i < 6; i++) {
			if (target.parentElement) {
				target = target.parentElement;
			}
		}
		const placeholder = document.createElement('div');
		placeholder.textContent = '{{ JSON CODE WILL BE HANDLED BY HUMAN }}';
		target.replaceWith(placeholder);
	});

	result = container.innerHTML.trim();
})();

copy(result);
console.clear();
`;

const outPath = resolve(__dirname, "browser-scrap-html.js");
await writeFile(outPath, browserScript, "utf-8");

console.log(
	`✅ Generated ${outPath} using path and innerHTML for slugs: ${targetSlugs.join(", ")}`,
);
