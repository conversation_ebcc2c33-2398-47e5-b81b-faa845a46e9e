**Task: Clean Up Dirty HTML (for Copy-Paste Use)**

I will provide messy or bloated HTML content. Please perform the following tasks carefully:

1. **Remove unnecessary HTML nesting** to simplify the structure.
2. **Preserve key semantic elements**, including:
   - Tables (`<table>`)
   - Headings (`<h1>`–`<h6>`)
   - Anchor tags (`<a>`)
   - Code blocks (`<code>` and `<pre>`)
3. If there are **code examples** (such as JSO<PERSON> in a "Responses" section), make sure they are wrapped in proper `<pre><code>` blocks.
4. If an element’s attribute value (e.g., a `class` or `id`) appears **randomly generated or non-semantic**, **remove it**.
5. **Return the cleaned HTML inside a raw HTML code block** (use triple backticks and `html`) so I can copy it directly.
6. Do **not render** the HTML visually in the chat UI. Your output must appear as plain HTML **text only** within the code block.

**Input HTML starts below this line:**
