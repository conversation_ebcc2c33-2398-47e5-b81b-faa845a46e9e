**Task: Convert HTML to Markdown (Preserve for Copy-Paste Use)**

You will be given HTML content. Follow the instructions below precisely:

1. **Convert all HTML into valid, clean Markdown**.
2. Preserve and translate the following structures accurately:
   - Headings (`<h1>` to `<h6>`).
   - Tables rules:
     - Use pipe syntax only, no bold formatting (e.g., `| col1 | col2 |`).
     - Table for "Request Parameters" should have 4 columns: `Parameter`, `Type`, `Required`, `Description`.
     - Table for "Payload Sample" (if any) should have 3 columns: `Field`, `Type`, `Description`.
     - Table for "Response Schema" should have 3 columns: `Field`, `Type`, `Description`.
   - Links (`<a>` tags).
   - Code blocks and inline code (`<pre>`, `<code>`).
3. **Wrap your entire Markdown output inside a Markdown code block** using triple backticks. This is critical for copy-paste.
4. **Do not render or preview** any part of the output visually. Show it **exactly as raw Markdown text**.
5. When your output includes Markdown code blocks (triple backticks), dod these to avoid breaking the outer code block:
   - Use alternate fencing (e.g., `~~~`) to avoid breaking the outer code block.
   - Maintain all whitespace, indentation, and line breaks **exactly as in the original HTML**.

**Input HTML starts below this line:**
