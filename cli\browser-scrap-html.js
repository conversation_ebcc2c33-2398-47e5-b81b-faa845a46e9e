
// This script is meant to be run in the browser console
const sidebarItems = [
	{
		"text": "Web Hooks",
		"path": "tag/WebHooks",
		"slug": "web-hooks",
		"children": [
			{
				"text": "List all subscribed webhooks.",
				"path": "tag/WebHooks/paths/~1{companyId}~1webhooks/get",
				"slug": "list-all-subscribed-webhooks",
				"children": []
			},
			{
				"text": "Subscribe to a webhook",
				"path": "tag/WebHooks/paths/~1{companyId}~1webhooks/put",
				"slug": "subscribe-to-a-webhook",
				"children": []
			},
			{
				"text": "Get a webhook subscription",
				"path": "tag/WebHooks/paths/~1{companyId}~1webhooks~1{webHookId}/get",
				"slug": "get-a-webhook-subscription",
				"children": []
			},
			{
				"text": "Unsubscribe from a webhook.",
				"path": "tag/WebHooks/paths/~1{companyId}~1webhooks~1{webHookId}/delete",
				"slug": "unsubscribe-from-a-webhook",
				"children": []
			}
		],
		"entity": "WebHooks"
	}
];

/**
 * Recursively scrape sections by path
 */
function scrapeSections(items) {
	let output = '';

	function collectContent(item) {
		const section = document.querySelector(`.api-content [data-section-id="${item.path}"]`);
		if (section) {
			output += section.innerHTML.trim() + '\n\n';
		}
		if (Array.isArray(item.children)) {
			item.children.forEach(collectContent);
		}
	}

	items.forEach(collectContent);
	return output.trim();
}

// Run the scraper
let result = scrapeSections(sidebarItems);

// === Post-processing: Remove .redoc-json and its 6-level ancestor ===
(function postProcess() {
	const container = document.createElement('div');
	container.innerHTML = result;

	const redocElements = container.querySelectorAll('.redoc-json');
	redocElements.forEach(el => {
		let target = el;
		for (let i = 0; i < 6; i++) {
			if (target.parentElement) {
				target = target.parentElement;
			}
		}
		const placeholder = document.createElement('div');
		placeholder.textContent = '{{ JSON CODE WILL BE HANDLED BY HUMAN }}';
		target.replaceWith(placeholder);
	});

	result = container.innerHTML.trim();
})();

copy(result);
console.clear();
