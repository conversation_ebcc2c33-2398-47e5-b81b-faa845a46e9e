## List customers

Relationships can be included via the `include` query parameter.

Options for filtering this list:  
- `filter[name]`  
- `filter[contactName]`  
- `filter[phone]`  
- `filter[email]`  
- `filter[website]`  
- `filter[address]`  
- `filter[city]`  
- `filter[state]`  
- `filter[postalCode]`  
- `filter[country]`  
- `filter[pricingSchemeId]`  
- `filter[defaultLocationId]`  
- `filter[isActive]`  
- `filter[smart]` (search across name and contact name)  
- `filter[lastOrder]` (number of days since the customer has last ordered)  

### Request endpoint

```http
GET /{companyId}/customers
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `Customer` objects:

| Field                   | Type                     | Description     |
|-------------------------|--------------------------|-----------------|
| addresses               | Array of objects         | All addresses for this customer. |
| balances                | Array of objects         | How much this customer owes you (potentially in one or more currencies). |
| contactName             | string                   | Name of your primary contact for this customer (if it's a business). |
| credits                 | Array of objects         | How much in store credit this customer has with you (potentially in one or more currencies). |
| customFields            | object (LargeCustomFieldValues) |          |
| customerId              | string <uuid>            | The primary identifier for this customer. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users. |
| defaultBillingAddress   | object (CustomerAddress) |                 |
| defaultBillingAddressId | string <uuid> Nullable   |                 |
| defaultCarrier          | string                   | The default shipment method for this customer. |
| defaultLocation         | object (Location)        |                 |
| defaultLocationId       | string <uuid> Nullable   |                 |
| defaultPaymentMethod    | string                   | The default payment method that this customer uses to pay you for sales orders. |
| defaultPaymentTerms     | object (PaymentTerms)    |                 |
| defaultPaymentTermsId   | string <uuid> Nullable   |                 |
| defaultSalesRep         | string                   | The sales rep for your company that should be assigned to orders from this customer by default. Note: this can only be set when legacy free-form sales rep values are allowed. |
| defaultSalesRepTeamMember | object (TeamMember)    |                 |
| defaultSalesRepTeamMemberId | string <uuid> Nullable |               |
| defaultShippingAddress  | object (CustomerAddress)  |                |
| defaultShippingAddressId | string <uuid> Nullable  |                 |
| discount                | string <decimal>         | Percentage discount that you give by default on orders by this customer. |
| dues                    | Array of objects         | How much this customer owes you (potentially in one or more currencies). |
| email                   | string                   | Primary contact email for this customer. |
| fax                     | string                   | Fax number for this customer. |
| isActive                | boolean                  | Vendors with `IsActive = false` are deactivated and hidden away for new usage. |
| lastModifiedBy          | object (TeamMember)      |                 |
| lastModifiedById        | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this customer. This is set automatically, and cannot be set through the API. |
| lastModifiedDttm        | string <date-time>       | The DateTimeOffset when this customer was last modified. This is set automatically, and cannot be set through the API. |
| name                    | string                   | Customer's name (human-readable, typically a person or business name). |
| orderHistory            | object (CustomerOrderHistory) |            |
| phone                   | string                   | Phone number for this customer. |
| pricingScheme           | object (PricingScheme)   |                 |
| pricingSchemeId          | string <uuid> Nullable   |                |
| remarks                 | string                   | Any additional remarks regarding this vendor. |
| taxExemptNumber         | string                   | A government number/identifier documenting why this customer has special tax privileges. |
| taxingScheme            | object (TaxingScheme)    |                 |
| taxingSchemeId          | string <uuid> Nullable   |                 |
| timestamp               | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| website                 | string                   | Vendor's website. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1467 lines long.
> > Please follow the link below to see response JSON.

[response-sample-of-list-customers.json](response-sample-of-list-customers.json)
