[{"text": "Overview", "path": "section/Overview", "slug": "overview", "children": [{"text": "Introduction", "path": "section/Overview/Introduction", "slug": "introduction", "children": []}, {"text": "Getting access to the API", "path": "section/Overview/Getting-access-to-the-API", "slug": "getting-access-to-the-api", "children": []}, {"text": "Support", "path": "section/Overview/Support", "slug": "support", "children": []}, {"text": "Endpoint", "path": "section/Overview/Endpoint", "slug": "endpoint", "children": []}, {"text": "HTTP headers fields", "path": "section/Overview/HTTP-headers-fields", "slug": "http-headers-fields", "children": []}, {"text": "Optional headers fields", "path": "section/Overview/Optional-headers-fields", "slug": "optional-headers-fields", "children": []}, {"text": "Read requests", "path": "section/Overview/Read-requests", "slug": "read-requests", "children": []}, {"text": "Write requests", "path": "section/Overview/Write-requests", "slug": "write-requests", "children": []}, {"text": "Rate limiting", "path": "section/Overview/Rate-limiting", "slug": "rate-limiting", "children": []}], "entity": "Overview"}, {"text": "Adjustment Reason", "path": "tag/AdjustmentReason", "slug": "adjustment-reason", "children": [{"text": "Get adjustment reasons", "path": "tag/AdjustmentReason/paths/~1{companyId}~1adjustment-reasons~1{adjustmentReasonId}/get", "slug": "get-adjustment-reasons", "children": []}, {"text": "List adjustment reasons", "path": "tag/AdjustmentReason/paths/~1{companyId}~1adjustment-reasons/get", "slug": "list-adjustment-reasons", "children": []}], "entity": "AdjustmentReason"}, {"text": "Categories", "path": "tag/Categories", "slug": "categories", "children": [{"text": "Get a category", "path": "tag/Categories/paths/~1{companyId}~1categories~1{categoryId}/get", "slug": "get-a-category", "children": []}, {"text": "List categories", "path": "tag/Categories/paths/~1{companyId}~1categories/get", "slug": "list-categories", "children": []}], "entity": "Categories"}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "path": "tag/Currency", "slug": "currency", "children": [{"text": "List currencies", "path": "tag/Currency/paths/~1{companyId}~1currencies/get", "slug": "list-currencies", "children": []}, {"text": "Get a currency", "path": "tag/Currency/paths/~1{companyId}~1currencies~1{currencyId}/get", "slug": "get-a-currency", "children": []}], "entity": "<PERSON><PERSON><PERSON><PERSON>"}, {"text": "Customer", "path": "tag/Customer", "slug": "customer", "children": [{"text": "Get a customer", "path": "tag/Customer/paths/~1{companyId}~1customers~1{customerId}/get", "slug": "get-a-customer", "children": []}, {"text": "List customers", "path": "tag/Customer/paths/~1{companyId}~1customers/get", "slug": "list-customers", "children": []}, {"text": "Insert or update a customer", "path": "tag/Customer/paths/~1{companyId}~1customers/put", "slug": "insert-or-update-a-customer", "children": []}], "entity": "Customer"}, {"text": "Custom Field Definitions", "path": "tag/CustomFieldDefinitions", "slug": "custom-field-definitions", "children": [{"text": "Get custom field definitions", "path": "tag/CustomFieldDefinitions/paths/~1{companyId}~1custom-field-definitions/get", "slug": "get-custom-field-definitions", "children": []}, {"text": "Insert or update custom field definition", "path": "tag/CustomFieldDefinitions/paths/~1{companyId}~1custom-field-definitions/put", "slug": "insert-or-update-custom-field-definition", "children": []}, {"text": "Get all dropdown custom field options for an entity", "path": "tag/CustomFieldDefinitions/paths/~1{companyId}~1custom-field-dropdown-options~1{entityType}/get", "slug": "get-all-dropdown-custom-field-options-for-an-entity", "children": []}, {"text": "Set custom field dropdown options", "path": "tag/CustomFieldDefinitions/paths/~1{companyId}~1custom-field-dropdown-options/put", "slug": "set-custom-field-dropdown-options", "children": []}], "entity": "CustomFieldDefinitions"}, {"text": "Custom Fields", "path": "tag/CustomFields", "slug": "custom-fields", "children": [{"text": "Get custom field labels", "path": "tag/CustomFields/paths/~1{companyId}~1custom-fields/get", "slug": "get-custom-field-labels", "children": []}, {"text": "Insert or Update custom field labels", "path": "tag/CustomFields/paths/~1{companyId}~1custom-fields/put", "slug": "insert-or-update-custom-field-labels", "children": []}], "entity": "CustomFields"}, {"text": "Location", "path": "tag/Location", "slug": "location", "children": [{"text": "Get a location", "path": "tag/Location/paths/~1{companyId}~1locations~1{locationId}/get", "slug": "get-a-location", "children": []}, {"text": "List locations", "path": "tag/Location/paths/~1{companyId}~1locations/get", "slug": "list-locations", "children": []}, {"text": "Get suggested sublocations", "path": "tag/Location/paths/~1{companyId}~1locations~1{locationId}~1suggested-sublocations/get", "slug": "get-suggested-sublocations", "children": []}, {"text": "Get suggested sublocations for a given product and location", "path": "tag/Location/paths/~1{companyId}~1locations~1{locationId}~1products~1{productId}~1suggested-sublocations/get", "slug": "get-suggested-sublocations-for-a-given-product-and-location", "children": []}], "entity": "Location"}, {"text": "Manufacturing Order", "path": "tag/ManufacturingOrder", "slug": "manufacturing-order", "children": [{"text": "Get a manufacture order", "path": "tag/ManufacturingOrder/paths/~1{companyId}~1manufacturing-orders~1{manufacturingOrderId}/get", "slug": "get-a-manufacture-order", "children": []}, {"text": "Insert or update a manufacture order", "path": "tag/ManufacturingOrder/paths/~1{companyId}~1manufacturing-orders/put", "slug": "insert-or-update-a-manufacture-order", "children": []}, {"text": "List manufacture orders", "path": "tag/ManufacturingOrder/paths/~1{companyId}~1manufacturing-orders/get", "slug": "list-manufacture-orders", "children": []}, {"text": "Get manufacturing order operation status", "path": "tag/ManufacturingOrder/paths/~1{companyId}~1manufacturing-orders~1{manufacturingOrderGuid}~1operation-statuses/get", "slug": "get-manufacturing-order-operation-status", "children": []}, {"text": "Insert or update manufacturing order operation status", "path": "tag/ManufacturingOrder/paths/~1{companyId}~1manufacturing-orders~1{manufacturingOrderGuid}~1operation-statuses/put", "slug": "insert-or-update-manufacturing-order-operation-status", "children": []}], "entity": "ManufacturingOrder"}, {"text": "Operation Type", "path": "tag/OperationType", "slug": "operation-type", "children": [{"text": "Get an Operation Type", "path": "tag/OperationType/paths/~1{companyId}~1operation-types~1{operationTypeId}/get", "slug": "get-an-operation-type", "children": []}, {"text": "List Operation Types", "path": "tag/OperationType/paths/~1{companyId}~1operation-types/get", "slug": "list-operation-types", "children": []}], "entity": "OperationType"}, {"text": "Payment Terms", "path": "tag/PaymentTerms", "slug": "payment-terms", "children": [{"text": "Get payment terms", "path": "tag/PaymentTerms/paths/~1{companyId}~1payment-terms~1{paymentTermsId}/get", "slug": "get-payment-terms", "children": []}, {"text": "List payment terms", "path": "tag/PaymentTerms/paths/~1{companyId}~1payment-terms/get", "slug": "list-payment-terms", "children": []}], "entity": "PaymentTerms"}, {"text": "Pricing Scheme", "path": "tag/PricingScheme", "slug": "pricing-scheme", "children": [{"text": "Get a pricing scheme", "path": "tag/PricingScheme/paths/~1{companyId}~1pricing-schemes~1{pricingSchemeId}/get", "slug": "get-a-pricing-scheme", "children": []}, {"text": "List pricing schemes", "path": "tag/PricingScheme/paths/~1{companyId}~1pricing-schemes/get", "slug": "list-pricing-schemes", "children": []}], "entity": "PricingScheme"}, {"text": "Product", "path": "tag/Product", "slug": "product", "children": [{"text": "Get a product", "path": "tag/Product/paths/~1{companyId}~1products~1{productId}/get", "slug": "get-a-product", "children": []}, {"text": "List products", "path": "tag/Product/paths/~1{companyId}~1products/get", "slug": "list-products", "children": []}, {"text": "Insert or update product", "path": "tag/Product/paths/~1{companyId}~1products/put", "slug": "insert-or-update-product", "children": []}, {"text": "Get product inventory summary", "path": "tag/Product/paths/~1{companyId}~1products~1{productId}~1summary/get", "slug": "get-product-inventory-summary", "children": []}, {"text": "Get multiple product inventory summaries", "path": "tag/Product/paths/~1{companyId}~1products~1summary/post", "slug": "get-multiple-product-inventory-summaries", "children": []}], "entity": "Product"}, {"text": "Product Cost Adjustment", "path": "tag/ProductCostAdjustment", "slug": "product-cost-adjustment", "children": [{"text": "Get a product cost adjustment", "path": "tag/ProductCostAdjustment/paths/~1{companyId}~1product-cost-adjustments~1{productCostAdjustmentId}/get", "slug": "get-a-product-cost-adjustment", "children": []}, {"text": "List product cost adjustments", "path": "tag/ProductCostAdjustment/paths/~1{companyId}~1product-cost-adjustments/get", "slug": "list-product-cost-adjustments", "children": []}, {"text": "Insert or update product cost adjustment", "path": "tag/ProductCostAdjustment/paths/~1{companyId}~1product-cost-adjustments/put", "slug": "insert-or-update-product-cost-adjustment", "children": []}], "entity": "ProductCostAdjustment"}, {"text": "Purchase Order", "path": "tag/PurchaseOrder", "slug": "purchase-order", "children": [{"text": "List purchase orders", "path": "tag/PurchaseOrder/paths/~1{companyId}~1purchase-orders/get", "slug": "list-purchase-orders", "children": []}, {"text": "Insert or update purchase order", "path": "tag/PurchaseOrder/paths/~1{companyId}~1purchase-orders/put", "slug": "insert-or-update-purchase-order", "children": []}, {"text": "Get a purchase order", "path": "tag/PurchaseOrder/paths/~1{companyId}~1purchase-orders~1{purchaseOrderId}/get", "slug": "get-a-purchase-order", "children": []}], "entity": "PurchaseOrder"}, {"text": "Sales Order", "path": "tag/SalesOrder", "slug": "sales-order", "children": [{"text": "Get a sales order", "path": "tag/SalesOrder/paths/~1{companyId}~1sales-orders~1{salesOrderId}/get", "slug": "get-a-sales-order", "children": []}, {"text": "List sales orders", "path": "tag/SalesOrder/paths/~1{companyId}~1sales-orders/get", "slug": "list-sales-orders", "children": []}, {"text": "Insert or update sales order", "path": "tag/SalesOrder/paths/~1{companyId}~1sales-orders/put", "slug": "insert-or-update-sales-order", "children": []}], "entity": "SalesOrder"}, {"text": "Stock Adjustment", "path": "tag/StockAdjustment", "slug": "stock-adjustment", "children": [{"text": "Get a stock adjustment", "path": "tag/StockAdjustment/paths/~1{companyId}~1stock-adjustments~1{stockAdjustmentId}/get", "slug": "get-a-stock-adjustment", "children": []}, {"text": "List stock adjustments", "path": "tag/StockAdjustment/paths/~1{companyId}~1stock-adjustments/get", "slug": "list-stock-adjustments", "children": []}, {"text": "Insert or update a stock adjustment", "path": "tag/StockAdjustment/paths/~1{companyId}~1stock-adjustments/put", "slug": "insert-or-update-a-stock-adjustment", "children": []}], "entity": "StockAdjustment"}, {"text": "Stock Count", "path": "tag/StockCount", "slug": "stock-count", "children": [{"text": "Get a stock count", "path": "tag/StockCount/paths/~1{companyId}~1stock-counts~1{stockCountId}/get", "slug": "get-a-stock-count", "children": []}, {"text": "List stock counts", "path": "tag/StockCount/paths/~1{companyId}~1stock-counts/get", "slug": "list-stock-counts", "children": []}, {"text": "Insert or update a stock count", "path": "tag/StockCount/paths/~1{companyId}~1stock-counts/put", "slug": "insert-or-update-a-stock-count", "children": []}, {"text": "Delete a count sheet", "path": "tag/StockCount/paths/~1{companyId}~1stock-counts~1{stockCountId}~1count-sheets~1{countSheetId}/delete", "slug": "delete-a-count-sheet", "children": []}, {"text": "Get a count sheet", "path": "tag/StockCount/paths/~1{companyId}~1count-sheets~1{countSheetId}/get", "slug": "get-a-count-sheet", "children": []}, {"text": "Insert or update a count sheet", "path": "tag/StockCount/paths/~1{companyId}~1stock-counts~1{stockCountId}~1count-sheet/put", "slug": "insert-or-update-a-count-sheet", "children": []}], "entity": "StockCount"}, {"text": "Stockroom Scan", "path": "tag/StockroomScan", "slug": "stockroom-scan", "children": [{"text": "List stockroom scans", "path": "tag/StockroomScan/paths/~1{companyId}~1stockroom-scans/get", "slug": "list-stockroom-scans", "children": []}, {"text": "Insert or update a stockroom scan", "path": "tag/StockroomScan/paths/~1{companyId}~1stockroom-scans/put", "slug": "insert-or-update-a-stockroom-scan", "children": []}], "entity": "StockroomScan"}, {"text": "Stockroom User", "path": "tag/StockroomUser", "slug": "stockroom-user", "children": [{"text": "Get stockroom users", "path": "tag/StockroomUser/paths/~1{companyId}~1stockroom-users~1{stockroomUserId}/get", "slug": "get-stockroom-users", "children": []}, {"text": "List stockroom users", "path": "tag/StockroomUser/paths/~1{companyId}~1stockroom-users/get", "slug": "list-stockroom-users", "children": []}], "entity": "StockroomUser"}, {"text": "Stock Transfer", "path": "tag/StockTransfer", "slug": "stock-transfer", "children": [{"text": "Get a stock transfer", "path": "tag/StockTransfer/paths/~1{companyId}~1stock-transfers~1{stockTransferId}/get", "slug": "get-a-stock-transfer", "children": []}, {"text": "List stock transfers", "path": "tag/StockTransfer/paths/~1{companyId}~1stock-transfers/get", "slug": "list-stock-transfers", "children": []}, {"text": "Insert or update a stock transfer", "path": "tag/StockTransfer/paths/~1{companyId}~1stock-transfers/put", "slug": "insert-or-update-a-stock-transfer", "children": []}], "entity": "StockTransfer"}, {"text": "Tax Code", "path": "tag/TaxCode", "slug": "tax-code", "children": [{"text": "Get a tax code", "path": "tag/TaxCode/paths/~1{companyId}~1tax-codes~1{taxCodeId}/get", "slug": "get-a-tax-code", "children": []}, {"text": "List tax codes", "path": "tag/TaxCode/paths/~1{companyId}~1tax-codes/get", "slug": "list-tax-codes", "children": []}], "entity": "TaxCode"}, {"text": "Taxing Scheme", "path": "tag/TaxingScheme", "slug": "taxing-scheme", "children": [{"text": "Get a taxing scheme", "path": "tag/TaxingScheme/paths/~1{companyId}~1taxing-schemes~1{taxingSchemeId}/get", "slug": "get-a-taxing-scheme", "children": []}, {"text": "List taxing schemes", "path": "tag/TaxingScheme/paths/~1{companyId}~1taxing-schemes/get", "slug": "list-taxing-schemes", "children": []}, {"text": "Insert or update taxing scheme", "path": "tag/TaxingScheme/paths/~1{companyId}~1taxing-schemes/put", "slug": "insert-or-update-taxing-scheme", "children": []}], "entity": "TaxingScheme"}, {"text": "Team Member", "path": "tag/TeamMember", "slug": "team-member", "children": [{"text": "List team members", "path": "tag/TeamMember/paths/~1{companyId}~1team-members/get", "slug": "list-team-members", "children": []}], "entity": "TeamMember"}, {"text": "<PERSON><PERSON><PERSON>", "path": "tag/Vendor", "slug": "vendor", "children": [{"text": "Get a vendor", "path": "tag/Vendor/paths/~1{companyId}~1vendors~1{vendorId}/get", "slug": "get-a-vendor", "children": []}, {"text": "List vendors", "path": "tag/Vendor/paths/~1{companyId}~1vendors/get", "slug": "list-vendors", "children": []}, {"text": "Insert or update a vendor", "path": "tag/Vendor/paths/~1{companyId}~1vendors/put", "slug": "insert-or-update-a-vendor", "children": []}], "entity": "<PERSON><PERSON><PERSON>"}, {"text": "Web Hooks", "path": "tag/WebHooks", "slug": "web-hooks", "children": [{"text": "List all subscribed webhooks.", "path": "tag/WebHooks/paths/~1{companyId}~1webhooks/get", "slug": "list-all-subscribed-webhooks", "children": []}, {"text": "Subscribe to a webhook", "path": "tag/WebHooks/paths/~1{companyId}~1webhooks/put", "slug": "subscribe-to-a-webhook", "children": []}, {"text": "Get a webhook subscription", "path": "tag/WebHooks/paths/~1{companyId}~1webhooks~1{webHookId}/get", "slug": "get-a-webhook-subscription", "children": []}, {"text": "Unsubscribe from a webhook.", "path": "tag/WebHooks/paths/~1{companyId}~1webhooks~1{webHookId}/delete", "slug": "unsubscribe-from-a-webhook", "children": []}], "entity": "WebHooks"}]