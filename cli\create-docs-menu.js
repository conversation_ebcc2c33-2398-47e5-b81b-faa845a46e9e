// generate-sidebar-links.js
import fs from 'fs/promises';
import path, { resolve } from 'path';
import { fileURLToPath } from 'url';

// Get __dirname in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const inputFile = resolve(__dirname, 'raw-docs-menu.json');
const outputDir = resolve(__dirname, '../content');
const outputFile = resolve(outputDir, 'docs-menu.md');

/**
 * Recursively generates markdown lines for sidebar items
 *
 * @param {SidebarTreeItem[]} items
 * @param {string|undefined} parentDirPath
 * @param {number} depth
 *
 * @returns {string[]}
 */
function generateMarkdown(items, parentDirPath = undefined, depth = 0) {
	const lines = [];

	for (const item of items) {
		const indent = '  '.repeat(depth);
		
		const dirPath = `${parentDirPath ? `${parentDirPath}/` : ''}${item.slug}`;
		const url = depth === 0 ? `${dirPath}/index.md` : (item.path.startsWith('section/Overview') ? `${parentDirPath}/index.md#${item.slug}` : `${dirPath}.md`);
		const link = `[${item.text}](${url})`;

		lines.push(`${indent}- ${link}`);

		if (Array.isArray(item.children) && item.children.length > 0) {
			lines.push(...generateMarkdown(item.children, dirPath, depth + 1));
		}
	}

	return lines;
}

async function main() {
	try {
		const jsonData = await fs.readFile(inputFile, 'utf8');
		const sidebarItems = JSON.parse(jsonData);

		const title = "# Inflow Inventory API Documentation\n\n";

		const markdownLines = generateMarkdown(sidebarItems);
		const markdownContent = title + markdownLines.join('\n') + '\n';

		await fs.mkdir(outputDir, { recursive: true });
		await fs.writeFile(outputFile, markdownContent, 'utf8');

		console.log(`✅ Markdown written to ${outputFile}`);
	} catch (err) {
		console.error('❌ Error generating sidebar links:', err);
	}
}

main();
